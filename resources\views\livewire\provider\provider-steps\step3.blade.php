@php
    use App\Models\User;
@endphp
<div>
    <x-form.input.text label="Phone" labelRequired="0" model="phone" type="tel" placeholder="Enter phone number" />
    <x-form.input.text label="Fax" labelRequired="0" model="fax" type="tel" placeholder="Enter fax number" />
    <x-form.input.text label="Address" labelRequired="1" model="address" placeholder="Enter street address" />
    <x-form.input.text label="City" labelRequired="1" model="city" placeholder="Enter city" />
    <div class="form-group" wire:ignore style="margin-bottom: 0">
        <label for="state" class="form-label">State</label> <span class="text-danger">*</span>
        <select wire:model="state_id" id="state" class="form-control">
            <option value="">Select State</option>
            @foreach ($states as $state)
                <option value="{{ $state->id }}">{{ $state->name }}
                </option>
            @endforeach
        </select>
    </div>
    <x-form.input.text label="ZIP Code" labelRequired="1" model="zip"
        placeholder="Enter ZIP code (12345 or 12345-6789)" />
</div>
@push('scripts')
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script>
        $(document).ready(function() {
            // Initialize Select2 for state dropdown
            $('#state_id').select2({
                placeholder: "Select State",
            }).on('change', function(e) {
                @this.set('state_id', $(e.target).val());
            });

        });
    </script>
@endpush
